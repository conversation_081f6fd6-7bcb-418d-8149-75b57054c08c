@echo off
echo Building PetSitter Connect API Only
echo ====================================

REM Check if .NET 8 is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET 8 SDK is not installed.
    echo Please install .NET 8 SDK from: https://dotnet.microsoft.com/download/dotnet/8.0
    exit /b 1
)

REM Check .NET version
for /f "tokens=*" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
echo ✅ .NET SDK Version: %DOTNET_VERSION%

REM Restore packages for Core
echo.
echo 📦 Restoring Core packages...
cd src\PetSitterConnect.Core
dotnet restore

if %errorlevel% neq 0 (
    echo ❌ Core package restore failed
    exit /b 1
)
echo ✅ Core package restore completed

REM Build Core
echo.
echo 🔨 Building Core project...
dotnet build --no-restore

if %errorlevel% neq 0 (
    echo ❌ Core project build failed
    exit /b 1
)
echo ✅ Core project built successfully

REM Restore packages for API
echo.
echo 📦 Restoring API packages...
cd ..\PetSitterConnect.Api
dotnet restore

if %errorlevel% neq 0 (
    echo ❌ API package restore failed
    exit /b 1
)
echo ✅ API package restore completed

REM Build API
echo.
echo 🔨 Building API project...
dotnet build --no-restore

if %errorlevel% neq 0 (
    echo ❌ API project build failed
    exit /b 1
)

echo ✅ API project built successfully
echo.
echo 🎉 API is ready to run!
echo.
echo 🚀 To start the API:
echo cd src\PetSitterConnect.Api ^&^& dotnet run
echo.
echo 📖 API Documentation will be available at: https://localhost:7001/swagger
