@echo off
echo PetSitter Connect - Build Script
echo =================================

REM Check if .NET 8 is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET 8 SDK is not installed.
    echo Please install .NET 8 SDK from: https://dotnet.microsoft.com/download/dotnet/8.0
    exit /b 1
)

REM Check .NET version
for /f "tokens=*" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
echo ✅ .NET SDK Version: %DOTNET_VERSION%

REM Restore packages
echo.
echo 📦 Restoring NuGet packages...
dotnet restore

if %errorlevel% neq 0 (
    echo ❌ Package restore failed
    exit /b 1
)
echo ✅ Package restore completed successfully

REM Build the solution
echo.
echo 🔨 Building the solution...
dotnet build --no-restore

if %errorlevel% neq 0 (
    echo ❌ Build failed
    exit /b 1
)

echo ✅ Build completed successfully
echo.
echo 🚀 Next steps:
echo 1. Start the API: cd src\PetSitterConnect.Api ^&^& dotnet run
echo 2. Start the MAUI app: cd src\PetSitterConnect ^&^& dotnet run
echo.
echo 📖 API Documentation will be available at: https://localhost:7001/swagger
