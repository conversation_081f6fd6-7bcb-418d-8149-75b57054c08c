{"version": 2, "dgSpecHash": "Luq/di+EJoU=", "success": true, "projectFilePath": "/Users/<USER>/Documents/PetCare /src/PetSitterConnect/PetSitterConnect.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/communitytoolkit.mvvm/8.2.2/communitytoolkit.mvvm.8.2.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/googlegson/2.10.1.4/googlegson.2.10.1.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.connections.abstractions/8.0.8/microsoft.aspnetcore.connections.abstractions.8.0.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.connections.client/8.0.8/microsoft.aspnetcore.http.connections.client.8.0.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.connections.common/8.0.8/microsoft.aspnetcore.http.connections.common.8.0.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.signalr.client/8.0.8/microsoft.aspnetcore.signalr.client.8.0.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.signalr.client.core/8.0.8/microsoft.aspnetcore.signalr.client.core.8.0.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.signalr.common/8.0.8/microsoft.aspnetcore.signalr.common.8.0.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.signalr.protocols.json/8.0.8/microsoft.aspnetcore.signalr.protocols.json.8.0.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration/8.0.0/microsoft.extensions.configuration.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/8.0.0/microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.binder/8.0.0/microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/8.0.0/microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/8.0.1/microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics/8.0.0/microsoft.extensions.diagnostics.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.abstractions/8.0.0/microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.features/8.0.8/microsoft.extensions.features.8.0.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.http/8.0.0/microsoft.extensions.http.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging/8.0.0/microsoft.extensions.logging.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/8.0.1/microsoft.extensions.logging.abstractions.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.debug/8.0.0/microsoft.extensions.logging.debug.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/8.0.2/microsoft.extensions.options.8.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options.configurationextensions/8.0.0/microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/8.0.0/microsoft.extensions.primitives.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.controls/8.0.91/microsoft.maui.controls.8.0.91.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.controls.build.tasks/8.0.91/microsoft.maui.controls.build.tasks.8.0.91.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.controls.compatibility/8.0.91/microsoft.maui.controls.compatibility.8.0.91.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.controls.core/8.0.91/microsoft.maui.controls.core.8.0.91.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.controls.xaml/8.0.91/microsoft.maui.controls.xaml.8.0.91.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.core/8.0.91/microsoft.maui.core.8.0.91.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.essentials/8.0.91/microsoft.maui.essentials.8.0.91.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.graphics/8.0.91/microsoft.maui.graphics.8.0.91.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.resizetizer/8.0.91/microsoft.maui.resizetizer.8.0.91.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.net.illink.tasks/8.0.17/microsoft.net.illink.tasks.8.0.17.nupkg.sha512", "/Users/<USER>/.nuget/packages/newtonsoft.json/13.0.3/newtonsoft.json.13.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.componentmodel.annotations/5.0.0/system.componentmodel.annotations.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/8.0.0/system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.pipelines/8.0.0/system.io.pipelines.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.channels/8.0.0/system.threading.channels.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.android.glide/********/xamarin.android.glide.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.android.glide.annotations/********/xamarin.android.glide.annotations.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.android.glide.disklrucache/********/xamarin.android.glide.disklrucache.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.android.glide.gifdecoder/********/xamarin.android.glide.gifdecoder.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.activity/*******/xamarin.androidx.activity.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.activity.ktx/*******/xamarin.androidx.activity.ktx.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.annotation/*******/xamarin.androidx.annotation.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.annotation.experimental/1.3.1.1/xamarin.androidx.annotation.experimental.1.3.1.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.annotation.jvm/1.6.0.2/xamarin.androidx.annotation.jvm.1.6.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.appcompat/1.6.1.3/xamarin.androidx.appcompat.1.6.1.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.appcompat.appcompatresources/1.6.1.3/xamarin.androidx.appcompat.appcompatresources.1.6.1.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.arch.core.common/2.2.0.3/xamarin.androidx.arch.core.common.2.2.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.arch.core.runtime/2.2.0.3/xamarin.androidx.arch.core.runtime.2.2.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.browser/1.5.0.3/xamarin.androidx.browser.1.5.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.cardview/********/xamarin.androidx.cardview.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.collection/1.2.0.9/xamarin.androidx.collection.1.2.0.9.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.collection.ktx/1.2.0.9/xamarin.androidx.collection.ktx.1.2.0.9.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.concurrent.futures/*******4/xamarin.androidx.concurrent.futures.*******4.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.constraintlayout/2.1.4.6/xamarin.androidx.constraintlayout.2.1.4.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.constraintlayout.core/1.0.4.6/xamarin.androidx.constraintlayout.core.1.0.4.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.coordinatorlayout/*******/xamarin.androidx.coordinatorlayout.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.core/1.10.1.2/xamarin.androidx.core.1.10.1.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.core.core.ktx/1.10.1.2/xamarin.androidx.core.core.ktx.1.10.1.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.cursoradapter/********/xamarin.androidx.cursoradapter.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.customview/*******8/xamarin.androidx.customview.*******8.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.customview.poolingcontainer/1.0.0.5/xamarin.androidx.customview.poolingcontainer.1.0.0.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.documentfile/1.0.1.19/xamarin.androidx.documentfile.1.0.1.19.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.drawerlayout/1.2.0.3/xamarin.androidx.drawerlayout.1.2.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.dynamicanimation/********/xamarin.androidx.dynamicanimation.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.emoji2/1.3.0.3/xamarin.androidx.emoji2.1.3.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.emoji2.viewshelper/1.3.0.3/xamarin.androidx.emoji2.viewshelper.1.3.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.exifinterface/1.3.6.2/xamarin.androidx.exifinterface.1.3.6.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.fragment/1.6.0.1/xamarin.androidx.fragment.1.6.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.fragment.ktx/1.6.0.1/xamarin.androidx.fragment.ktx.1.6.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.interpolator/********/xamarin.androidx.interpolator.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.legacy.support.core.utils/********/xamarin.androidx.legacy.support.core.utils.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.common/2.6.1.3/xamarin.androidx.lifecycle.common.2.6.1.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata/2.6.1.3/xamarin.androidx.lifecycle.livedata.2.6.1.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata.core/2.6.1.3/xamarin.androidx.lifecycle.livedata.core.2.6.1.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata.core.ktx/2.6.1.3/xamarin.androidx.lifecycle.livedata.core.ktx.2.6.1.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.process/2.6.1.3/xamarin.androidx.lifecycle.process.2.6.1.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime/2.6.1.3/xamarin.androidx.lifecycle.runtime.2.6.1.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.ktx/2.6.1.3/xamarin.androidx.lifecycle.runtime.ktx.2.6.1.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel/2.6.1.3/xamarin.androidx.lifecycle.viewmodel.2.6.1.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel.ktx/2.6.1.3/xamarin.androidx.lifecycle.viewmodel.ktx.2.6.1.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodelsavedstate/2.6.1.3/xamarin.androidx.lifecycle.viewmodelsavedstate.2.6.1.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.loader/*******9/xamarin.androidx.loader.*******9.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.localbroadcastmanager/1.1.0.7/xamarin.androidx.localbroadcastmanager.1.1.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.common/2.6.0.1/xamarin.androidx.navigation.common.2.6.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.fragment/2.6.0.1/xamarin.androidx.navigation.fragment.2.6.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.runtime/2.6.0.1/xamarin.androidx.navigation.runtime.2.6.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.ui/2.6.0.1/xamarin.androidx.navigation.ui.2.6.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.print/********/xamarin.androidx.print.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.profileinstaller.profileinstaller/1.3.1.2/xamarin.androidx.profileinstaller.profileinstaller.1.3.1.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.recyclerview/1.3.0.3/xamarin.androidx.recyclerview.1.3.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.resourceinspection.annotation/1.0.1.7/xamarin.androidx.resourceinspection.annotation.1.0.1.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.savedstate/*******/xamarin.androidx.savedstate.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.savedstate.savedstate.ktx/*******/xamarin.androidx.savedstate.savedstate.ktx.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.security.securitycrypto/*******-alpha06/xamarin.androidx.security.securitycrypto.*******-alpha06.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.slidingpanelayout/*******/xamarin.androidx.slidingpanelayout.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.startup.startupruntime/*******/xamarin.androidx.startup.startupruntime.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.swiperefreshlayout/*******4/xamarin.androidx.swiperefreshlayout.*******4.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.tracing.tracing/*******/xamarin.androidx.tracing.tracing.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.transition/********/xamarin.androidx.transition.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.vectordrawable/*******9/xamarin.androidx.vectordrawable.*******9.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.vectordrawable.animated/*******9/xamarin.androidx.vectordrawable.animated.*******9.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.versionedparcelable/********/xamarin.androidx.versionedparcelable.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.viewpager/********/xamarin.androidx.viewpager.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.viewpager2/********/xamarin.androidx.viewpager2.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.window/*******/xamarin.androidx.window.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.window.extensions.core.core/1.0.0.1/xamarin.androidx.window.extensions.core.core.1.0.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.google.android.material/1.9.0.2/xamarin.google.android.material.1.9.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.google.code.findbugs.jsr305/3.0.2.9/xamarin.google.code.findbugs.jsr305.3.0.2.9.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.google.crypto.tink.android/1.10.0/xamarin.google.crypto.tink.android.1.10.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.google.errorprone.annotations/2.20.0.1/xamarin.google.errorprone.annotations.2.20.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.google.guava.listenablefuture/1.0.0.14/xamarin.google.guava.listenablefuture.1.0.0.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.jetbrains.annotations/24.0.1.3/xamarin.jetbrains.annotations.24.0.1.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.kotlin.stdlib/1.9.0.1/xamarin.kotlin.stdlib.1.9.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.kotlin.stdlib.common/1.9.0.1/xamarin.kotlin.stdlib.common.1.9.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.kotlin.stdlib.jdk7/1.9.0.1/xamarin.kotlin.stdlib.jdk7.1.9.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.kotlin.stdlib.jdk8/1.9.0.1/xamarin.kotlin.stdlib.jdk8.1.9.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.kotlinx.coroutines.android/*******/xamarin.kotlinx.coroutines.android.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.kotlinx.coroutines.core.jvm/*******/xamarin.kotlinx.coroutines.core.jvm.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.17/microsoft.netcore.app.ref.8.0.17.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/8.0.17/microsoft.aspnetcore.app.ref.8.0.17.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.17/microsoft.netcore.app.ref.8.0.17.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/8.0.17/microsoft.aspnetcore.app.ref.8.0.17.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.ios.ref.net8.0_18.0/18.0.8324/microsoft.ios.ref.net8.0_18.0.18.0.8324.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.ios.runtime.iossimulator-arm64.net8.0_18.0/18.0.8324/microsoft.ios.runtime.iossimulator-arm64.net8.0_18.0.18.0.8324.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.17/microsoft.netcore.app.ref.8.0.17.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/8.0.17/microsoft.aspnetcore.app.ref.8.0.17.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maccatalyst.ref.net8.0_18.0/18.0.8324/microsoft.maccatalyst.ref.net8.0_18.0.18.0.8324.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maccatalyst.runtime.maccatalyst-arm64.net8.0_18.0/18.0.8324/microsoft.maccatalyst.runtime.maccatalyst-arm64.net8.0_18.0.18.0.8324.nupkg.sha512"], "logs": []}