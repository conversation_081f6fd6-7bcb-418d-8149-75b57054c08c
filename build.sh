#!/bin/bash

echo "PetSitter Connect - Build Script"
echo "================================="

# Check if .NET 8 is installed
if ! command -v dotnet &> /dev/null; then
    echo "❌ .NET 8 SDK is not installed."
    echo "Please install .NET 8 SDK from: https://dotnet.microsoft.com/download/dotnet/8.0"
    exit 1
fi

# Check .NET version
DOTNET_VERSION=$(dotnet --version)
echo "✅ .NET SDK Version: $DOTNET_VERSION"

# Restore packages
echo ""
echo "📦 Restoring NuGet packages..."
dotnet restore

if [ $? -eq 0 ]; then
    echo "✅ Package restore completed successfully"
else
    echo "❌ Package restore failed"
    exit 1
fi

# Build the solution
echo ""
echo "🔨 Building the solution..."
dotnet build --no-restore

if [ $? -eq 0 ]; then
    echo "✅ Build completed successfully"
    echo ""
    echo "🚀 Next steps:"
    echo "1. Start the API: cd src/PetSitterConnect.Api && dotnet run"
    echo "2. Start the MAUI app: cd src/PetSitterConnect && dotnet run"
    echo ""
    echo "📖 API Documentation will be available at: https://localhost:7001/swagger"
else
    echo "❌ Build failed"
    exit 1
fi
