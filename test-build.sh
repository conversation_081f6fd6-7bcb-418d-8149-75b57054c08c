#!/bin/bash

echo "Testing PetSitter Connect Build"
echo "==============================="

# Check if we're in the right directory
if [ ! -f "PetSitterConnect.sln" ]; then
    echo "❌ Error: PetSitterConnect.sln not found. Please run this script from the project root."
    exit 1
fi

# Check if .NET 8 is installed
if ! command -v dotnet &> /dev/null; then
    echo "❌ .NET 8 SDK is not installed."
    echo "Please install .NET 8 SDK from: https://dotnet.microsoft.com/download/dotnet/8.0"
    exit 1
fi

# Check .NET version
DOTNET_VERSION=$(dotnet --version)
echo "✅ .NET SDK Version: $DOTNET_VERSION"

# Clean previous builds
echo ""
echo "🧹 Cleaning previous builds..."
dotnet clean

# Restore packages
echo ""
echo "📦 Restoring NuGet packages..."
dotnet restore

if [ $? -eq 0 ]; then
    echo "✅ Package restore completed successfully"
else
    echo "❌ Package restore failed"
    exit 1
fi

# Build the Core project first
echo ""
echo "🔨 Building Core project..."
dotnet build src/PetSitterConnect.Core --no-restore

if [ $? -eq 0 ]; then
    echo "✅ Core project built successfully"
else
    echo "❌ Core project build failed"
    exit 1
fi

# Build the API project
echo ""
echo "🔨 Building API project..."
dotnet build src/PetSitterConnect.Api --no-restore

if [ $? -eq 0 ]; then
    echo "✅ API project built successfully"
else
    echo "❌ API project build failed"
    exit 1
fi

# Try to build the MAUI project
echo ""
echo "🔨 Building MAUI project..."
dotnet build src/PetSitterConnect --no-restore

if [ $? -eq 0 ]; then
    echo "✅ MAUI project built successfully"
    echo ""
    echo "🎉 All projects built successfully!"
    echo ""
    echo "🚀 Next steps:"
    echo "1. Start the API: cd src/PetSitterConnect.Api && dotnet run"
    echo "2. Start the MAUI app: cd src/PetSitterConnect && dotnet run"
else
    echo "❌ MAUI project build failed"
    echo ""
    echo "🔍 This might be due to missing MAUI workloads."
    echo "Try installing MAUI workloads with:"
    echo "dotnet workload install maui"
    exit 1
fi
