#!/bin/bash

echo "Building PetSitter Connect API Only"
echo "===================================="

# Check if .NET 8 is installed
if ! command -v dotnet &> /dev/null; then
    echo "❌ .NET 8 SDK is not installed."
    echo "Please install .NET 8 SDK from: https://dotnet.microsoft.com/download/dotnet/8.0"
    exit 1
fi

# Check .NET version
DOTNET_VERSION=$(dotnet --version)
echo "✅ .NET SDK Version: $DOTNET_VERSION"

# Restore packages for Core
echo ""
echo "📦 Restoring Core packages..."
cd src/PetSitterConnect.Core
dotnet restore

if [ $? -eq 0 ]; then
    echo "✅ Core package restore completed"
else
    echo "❌ Core package restore failed"
    exit 1
fi

# Build Core
echo ""
echo "🔨 Building Core project..."
dotnet build --no-restore

if [ $? -eq 0 ]; then
    echo "✅ Core project built successfully"
else
    echo "❌ Core project build failed"
    exit 1
fi

# Restore packages for API
echo ""
echo "📦 Restoring API packages..."
cd ../PetSitterConnect.Api
dotnet restore

if [ $? -eq 0 ]; then
    echo "✅ API package restore completed"
else
    echo "❌ API package restore failed"
    exit 1
fi

# Build API
echo ""
echo "🔨 Building API project..."
dotnet build --no-restore

if [ $? -eq 0 ]; then
    echo "✅ API project built successfully"
    echo ""
    echo "🎉 API is ready to run!"
    echo ""
    echo "🚀 To start the API:"
    echo "cd src/PetSitterConnect.Api && dotnet run"
    echo ""
    echo "📖 API Documentation will be available at: https://localhost:7001/swagger"
else
    echo "❌ API project build failed"
    exit 1
fi
